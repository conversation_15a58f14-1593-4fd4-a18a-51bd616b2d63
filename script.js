// Timer functionality
let timeLeft = 90 * 60; // 90 minutes in seconds
let timerInterval;

function startTimer() {
    timerInterval = setInterval(() => {
        timeLeft--;
        updateTimerDisplay();
        
        if (timeLeft <= 0) {
            clearInterval(timerInterval);
            alert("Time's up! Great job everyone! 🎉");
        }
    }, 1000);
}

function updateTimerDisplay() {
    const minutes = Math.floor(timeLeft / 60);
    const seconds = timeLeft % 60;
    const display = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    document.getElementById('timer').textContent = display;
    
    // Change color as time runs out
    const timerElement = document.getElementById('timer');
    if (timeLeft < 600) { // Last 10 minutes
        timerElement.style.background = '#ff6b6b';
    } else if (timeLeft < 1800) { // Last 30 minutes
        timerElement.style.background = '#ffa726';
    }
}

// Section navigation
function showSection(sectionId) {
    // Hide all sections
    const sections = document.querySelectorAll('.lesson-section');
    sections.forEach(section => {
        section.classList.remove('active');
    });
    
    // Remove active class from all nav buttons
    const navButtons = document.querySelectorAll('.nav-btn');
    navButtons.forEach(btn => {
        btn.classList.remove('active');
    });
    
    // Show selected section
    document.getElementById(sectionId).classList.add('active');
    
    // Add active class to clicked button
    event.target.classList.add('active');
    
    // Play section transition sound (if audio is enabled)
    playTransitionSound();
}

function playTransitionSound() {
    // Create a simple beep sound using Web Audio API
    try {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        oscillator.frequency.value = 800;
        oscillator.type = 'sine';
        
        gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
        
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.1);
    } catch (e) {
        // Audio not supported, continue silently
    }
}

// Warm-up activities
function revealOriginal(clipNumber) {
    const originalElement = document.getElementById(`original${clipNumber}`);
    originalElement.classList.remove('hidden');
    
    // Add celebration animation
    originalElement.style.animation = 'fadeIn 0.5s ease-in';
    
    // Change button text
    event.target.textContent = "Great job! 🎉";
    event.target.style.background = '#48bb78';
    event.target.disabled = true;
}

// Discovery section
function showDemoAnswer() {
    const answerElement = document.getElementById('demo-answer');
    answerElement.textContent = "she was going to the market that day";
    answerElement.style.background = '#c6f6d5';
    answerElement.style.color = '#2f855a';
    
    // Show explanation
    const explanation = document.createElement('div');
    explanation.innerHTML = `
        <div style="margin-top: 15px; padding: 10px; background: #e6fffa; border-radius: 5px;">
            <strong>Explanation:</strong><br>
            • "I am" → "she was" (person + tense change)<br>
            • "today" → "that day" (time change)
        </div>
    `;
    answerElement.parentNode.appendChild(explanation);
    
    event.target.textContent = "Perfect! 🌟";
    event.target.style.background = '#48bb78';
    event.target.disabled = true;
}

// Puzzle functionality
const puzzleAnswers = {
    1: {
        'A': 'Linh', // "I visited my grandmother yesterday." → Linh told me that she had visited her grandmother the day before.
        'B': 'Nam',  // "I will study harder next semester." → Nam said that he would study harder next semester.
        'C': 'Duc'   // "I am feeling tired today." → Duc mentioned that he was feeling tired that day.
    }
};

let puzzleAttempts = {};

function checkMatch(puzzleNumber, option) {
    if (!puzzleAttempts[puzzleNumber]) {
        puzzleAttempts[puzzleNumber] = 0;
    }
    
    puzzleAttempts[puzzleNumber]++;
    
    const correctAnswer = puzzleAnswers[puzzleNumber][option];
    const button = event.target;
    
    // Simple check - in a real implementation, you'd have more sophisticated matching
    let isCorrect = false;
    
    if (option === 'A' && correctAnswer === 'Linh') isCorrect = true;
    if (option === 'B' && correctAnswer === 'Nam') isCorrect = true;
    if (option === 'C' && correctAnswer === 'Duc') isCorrect = true;
    
    if (isCorrect) {
        button.classList.add('correct');
        button.textContent += ' ✓ Correct!';
        button.disabled = true;
        
        // Show explanation
        showPuzzleExplanation(puzzleNumber, option);
    } else {
        button.classList.add('incorrect');
        button.textContent += ' ✗ Try again!';
        
        setTimeout(() => {
            button.classList.remove('incorrect');
            button.textContent = button.textContent.replace(' ✗ Try again!', '');
        }, 1500);
    }
    
    // Give hints after multiple attempts
    if (puzzleAttempts[puzzleNumber] >= 2 && !isCorrect) {
        showHint(puzzleNumber, option);
    }
}

function showPuzzleExplanation(puzzleNumber, option) {
    const explanations = {
        1: {
            'A': 'Correct! "Yesterday" becomes "the day before" in reported speech.',
            'B': 'Correct! "Will" becomes "would" in reported speech.',
            'C': 'Correct! "Today" becomes "that day" in reported speech.'
        }
    };
    
    const explanation = document.createElement('div');
    explanation.innerHTML = `
        <div style="margin-top: 10px; padding: 10px; background: #c6f6d5; border-radius: 5px; border-left: 4px solid #38a169;">
            <strong>🎯 ${explanations[puzzleNumber][option]}</strong>
        </div>
    `;
    
    event.target.parentNode.appendChild(explanation);
}

function showHint(puzzleNumber, option) {
    const hints = {
        1: {
            'A': 'Hint: Look for time words that change in reported speech!',
            'B': 'Hint: Future tense verbs change in reported speech!',
            'C': 'Hint: Present tense and time words both change!'
        }
    };

    const hint = document.createElement('div');
    hint.innerHTML = `
        <div style="margin-top: 10px; padding: 10px; background: #fff3cd; border-radius: 5px; border-left: 4px solid #ffc107;">
            <strong>💡 ${hints[puzzleNumber][option]}</strong>
        </div>
    `;

    event.target.parentNode.appendChild(hint);
}

// Additional puzzle functions
function checkTimeFix(puzzleNumber, option) {
    const button = event.target;

    // Correct answer is B: "The student said that he would finish his project the next day."
    if (option === 'B') {
        button.classList.add('correct');
        button.textContent += ' ✓ Perfect!';
        button.disabled = true;

        // Show explanation
        const explanation = document.createElement('div');
        explanation.innerHTML = `
            <div style="margin-top: 10px; padding: 10px; background: #c6f6d5; border-radius: 5px; border-left: 4px solid #38a169;">
                <strong>🎯 Excellent!</strong><br>
                • "will" → "would" (future to conditional)<br>
                • "tomorrow" → "the next day" (time change)<br>
                • "I" → "he" (person change)
            </div>
        `;
        button.parentNode.appendChild(explanation);

        // Disable other buttons
        const allButtons = button.parentNode.querySelectorAll('.fix-btn');
        allButtons.forEach(btn => {
            if (btn !== button) {
                btn.disabled = true;
                btn.style.opacity = '0.5';
            }
        });
    } else {
        button.classList.add('incorrect');
        button.textContent += ' ✗ Try again!';

        setTimeout(() => {
            button.classList.remove('incorrect');
            button.textContent = button.textContent.replace(' ✗ Try again!', '');
        }, 1500);

        // Give specific feedback
        const feedback = {
            'A': 'Remember: "tomorrow" needs to change when reporting later!',
            'C': 'Remember: "will" changes to "would" in reported speech!'
        };

        if (feedback[option]) {
            const hint = document.createElement('div');
            hint.innerHTML = `
                <div style="margin-top: 10px; padding: 10px; background: #fff3cd; border-radius: 5px; border-left: 4px solid #ffc107;">
                    <strong>💡 ${feedback[option]}</strong>
                </div>
            `;
            button.parentNode.appendChild(hint);

            setTimeout(() => {
                hint.remove();
            }, 3000);
        }
    }
}

function checkQuestionTransform(puzzleNumber, option) {
    const button = event.target;

    // Correct answer is C: "The reporter asked where he lived."
    if (option === 'C') {
        button.classList.add('correct');
        button.textContent += ' ✓ Brilliant!';
        button.disabled = true;

        // Show explanation
        const explanation = document.createElement('div');
        explanation.innerHTML = `
            <div style="margin-top: 10px; padding: 10px; background: #c6f6d5; border-radius: 5px; border-left: 4px solid #38a169;">
                <strong>🎯 Outstanding!</strong><br>
                • Questions become statements in reported speech<br>
                • "do you" → "he" (person change)<br>
                • "live" → "lived" (tense change)<br>
                • No question mark needed!
            </div>
        `;
        button.parentNode.appendChild(explanation);

        // Disable other buttons
        const allButtons = button.parentNode.querySelectorAll('.transform-btn');
        allButtons.forEach(btn => {
            if (btn !== button) {
                btn.disabled = true;
                btn.style.opacity = '0.5';
            }
        });
    } else {
        button.classList.add('incorrect');
        button.textContent += ' ✗ Not quite!';

        setTimeout(() => {
            button.classList.remove('incorrect');
            button.textContent = button.textContent.replace(' ✗ Not quite!', '');
        }, 1500);

        // Give specific feedback
        const feedback = {
            'A': 'Questions don\'t stay as questions in reported speech!',
            'B': 'Remember to change the person from "you" to "he/she"!'
        };

        if (feedback[option]) {
            const hint = document.createElement('div');
            hint.innerHTML = `
                <div style="margin-top: 10px; padding: 10px; background: #fff3cd; border-radius: 5px; border-left: 4px solid #ffc107;">
                    <strong>💡 ${feedback[option]}</strong>
                </div>
            `;
            button.parentNode.appendChild(hint);

            setTimeout(() => {
                hint.remove();
            }, 3000);
        }
    }
}

// Project timer for group work
let projectTimer = 20 * 60; // 20 minutes
let projectInterval;

function startProjectTimer() {
    projectInterval = setInterval(() => {
        projectTimer--;
        updateProjectTimerDisplay();
        
        if (projectTimer <= 0) {
            clearInterval(projectInterval);
            alert("Project time is up! Time to present! 🎬");
        }
    }, 1000);
}

function updateProjectTimerDisplay() {
    const minutes = Math.floor(projectTimer / 60);
    const seconds = projectTimer % 60;
    const display = `Project Time: ${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    
    // Create or update project timer display
    let projectTimerElement = document.getElementById('project-timer');
    if (!projectTimerElement) {
        projectTimerElement = document.createElement('div');
        projectTimerElement.id = 'project-timer';
        projectTimerElement.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4299e1;
            color: white;
            padding: 10px 15px;
            border-radius: 20px;
            font-weight: bold;
            z-index: 1000;
        `;
        document.body.appendChild(projectTimerElement);
    }
    
    projectTimerElement.textContent = display;
    
    // Change color as time runs out
    if (projectTimer < 300) { // Last 5 minutes
        projectTimerElement.style.background = '#ff6b6b';
    } else if (projectTimer < 600) { // Last 10 minutes
        projectTimerElement.style.background = '#ffa726';
    }
}

// Presentation evaluation
function startPresentation() {
    // Hide project timer
    const projectTimerElement = document.getElementById('project-timer');
    if (projectTimerElement) {
        projectTimerElement.style.display = 'none';
    }
    
    // Show presentation interface
    showPresentationInterface();
}

function showPresentationInterface() {
    const presentationSection = document.getElementById('presentation');
    
    // Add presentation controls
    const controls = document.createElement('div');
    controls.innerHTML = `
        <div style="background: #f7fafc; border-radius: 10px; padding: 20px; margin: 20px 0;">
            <h4>🎬 Presentation Controls</h4>
            <div style="display: flex; gap: 10px; margin: 15px 0; flex-wrap: wrap;">
                <button onclick="nextTeam()" style="background: #4299e1; color: white; border: none; padding: 10px 20px; border-radius: 20px; cursor: pointer;">Next Team</button>
                <button onclick="startTeamTimer()" style="background: #48bb78; color: white; border: none; padding: 10px 20px; border-radius: 20px; cursor: pointer;">Start 4-min Timer</button>
                <button onclick="showFeedback()" style="background: #ed8936; color: white; border: none; padding: 10px 20px; border-radius: 20px; cursor: pointer;">Give Feedback</button>
            </div>
            <div id="team-timer" style="font-size: 1.5em; font-weight: bold; text-align: center; margin: 10px 0;"></div>
        </div>
    `;
    
    presentationSection.appendChild(controls);
}

let teamNumber = 1;
let teamTime = 4 * 60; // 4 minutes per team
let teamInterval;

function nextTeam() {
    teamNumber++;
    document.getElementById('team-timer').textContent = `Team ${teamNumber} - Get Ready! 🎤`;
    
    // Reset timer
    teamTime = 4 * 60;
    if (teamInterval) {
        clearInterval(teamInterval);
    }
}

function startTeamTimer() {
    teamInterval = setInterval(() => {
        teamTime--;
        const minutes = Math.floor(teamTime / 60);
        const seconds = teamTime % 60;
        const display = `Team ${teamNumber}: ${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        document.getElementById('team-timer').textContent = display;
        
        if (teamTime <= 0) {
            clearInterval(teamInterval);
            document.getElementById('team-timer').textContent = `Team ${teamNumber} - Time's up! 👏`;
        }
    }, 1000);
}

function showFeedback() {
    const feedback = prompt("Quick feedback for this team (optional):");
    if (feedback) {
        const feedbackDiv = document.createElement('div');
        feedbackDiv.innerHTML = `
            <div style="background: #e6fffa; border: 1px solid #38b2ac; border-radius: 5px; padding: 10px; margin: 10px 0;">
                <strong>Team ${teamNumber} Feedback:</strong> ${feedback}
            </div>
        `;
        document.getElementById('team-timer').parentNode.appendChild(feedbackDiv);
    }
}

// Initialize the lesson
document.addEventListener('DOMContentLoaded', function() {
    startTimer();
    
    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey) {
            switch(e.key) {
                case '1':
                    showSection('warmup');
                    break;
                case '2':
                    showSection('discovery');
                    break;
                case '3':
                    showSection('practice');
                    break;
                case '4':
                    showSection('puzzles');
                    break;
                case '5':
                    showSection('projects');
                    break;
                case '6':
                    showSection('presentation');
                    break;
            }
        }
    });
    
    // Auto-start project timer when projects section is opened
    const projectsNavBtn = document.querySelector('[onclick="showSection(\'projects\')"]');
    if (projectsNavBtn) {
        projectsNavBtn.addEventListener('click', function() {
            setTimeout(() => {
                if (!projectInterval) {
                    startProjectTimer();
                }
            }, 1000);
        });
    }
    
    // Auto-start presentation when presentation section is opened
    const presentationNavBtn = document.querySelector('[onclick="showSection(\'presentation\')"]');
    if (presentationNavBtn) {
        presentationNavBtn.addEventListener('click', function() {
            setTimeout(() => {
                startPresentation();
            }, 1000);
        });
    }
});

// Add some fun interactions
function addCelebration() {
    // Create confetti effect
    for (let i = 0; i < 50; i++) {
        const confetti = document.createElement('div');
        confetti.style.cssText = `
            position: fixed;
            width: 10px;
            height: 10px;
            background: ${['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57'][Math.floor(Math.random() * 5)]};
            top: -10px;
            left: ${Math.random() * 100}vw;
            z-index: 9999;
            pointer-events: none;
            animation: fall ${Math.random() * 3 + 2}s linear forwards;
        `;
        
        document.body.appendChild(confetti);
        
        setTimeout(() => {
            confetti.remove();
        }, 5000);
    }
}

// Add CSS for confetti animation
const style = document.createElement('style');
style.textContent = `
    @keyframes fall {
        to {
            transform: translateY(100vh) rotate(360deg);
        }
    }
`;
document.head.appendChild(style);
