# 🎤 Reported Speech Adventure - Interactive Lesson

## 📖 Overview
An interactive 90-minute lesson for Vietnamese B1 level teenagers learning reported speech through a fun news reporter theme. Based on Unit 6B curriculum with focus on spoken English, comprehension, coherence, and fluency.

## 🚀 Quick Start
1. Open `index.html` in a web browser
2. Project on classroom screen/smartboard
3. Follow the teacher's guide (`teacher-guide.md`)
4. Navigate through sections using the top navigation buttons

## 📋 Lesson Structure (90 minutes)

### 🔥 Warm-up (10 min)
- Interactive "What Did They Say?" activity
- Students discover reported speech patterns
- News reporter context introduction

### 🔍 Discovery (15 min)
- Reporter's Toolkit with transformation rules
- Interactive demo with immediate feedback
- Time, person, and verb change patterns

### 💪 Practice (20 min)
- "Circus of Conversations" moving activity
- 3 stations with different conversation types
- Peer-to-peer practice with real communication

### 🧩 Puzzles (15 min)
- 3 logic puzzles with increasing difficulty
- Immediate feedback and hints
- Gamified learning experience

### 📺 Projects (20 min)
- Team-based news report creation
- Choice between school events or celebrity interviews
- Built-in timer and language support

### 🎬 Presentations (30 min)
- Live news broadcast simulation
- 4-minute presentations per team
- Evaluation based on comprehension, coherence, fluency

## 🎯 Key Features

### ✨ Interactive Elements
- **Timer System:** 90-minute main timer + project timer
- **Navigation:** Easy section switching with keyboard shortcuts
- **Immediate Feedback:** Instant responses to student choices
- **Gamification:** Points, celebrations, and progress tracking

### 🎪 No Writing Required
- All activities are speaking-focused
- Interactive buttons and verbal responses
- Movement-based practice activities
- Presentation-centered final assessment

### 🧩 Challenging but Achievable
- Logic puzzles designed for B1 level
- Scaffolded difficulty progression
- Hint system for struggling students
- Multiple attempt opportunities

### 📺 News Reporter Theme
- Consistent professional context
- Sample reporter language provided
- Real-world application focus
- Engaging career-based scenarios

## 🎮 Controls & Navigation

### Keyboard Shortcuts
- `Ctrl + 1`: Warm-up section
- `Ctrl + 2`: Discovery section
- `Ctrl + 3`: Practice section
- `Ctrl + 4`: Puzzles section
- `Ctrl + 5`: Projects section
- `Ctrl + 6`: Presentations section

### Built-in Timers
- **Main Timer:** 90 minutes (top right)
- **Project Timer:** 20 minutes (appears during projects)
- **Team Timer:** 4 minutes per presentation

## 📊 Assessment Criteria

### Comprehension (33%)
- Correct use of reported speech transformations
- Understanding of time, person, and verb changes
- Accurate question-to-statement conversions

### Coherence (33%)
- Logical flow and organization
- Clear transitions between ideas
- Structured presentation delivery

### Fluency (33%)
- Natural pace and rhythm
- Confident delivery
- Engaging presentation style

## 🎯 Learning Objectives

Students will be able to:
- Transform direct speech into reported speech accurately
- Use appropriate time and person changes in context
- Report questions and statements naturally
- Present news reports using reported speech confidently
- Apply reported speech in real communication situations

## 🔧 Technical Requirements

### Minimum Requirements
- Modern web browser (Chrome, Firefox, Safari, Edge)
- Projector or large screen for classroom display
- Audio capability for sound effects (optional)

### Recommended Setup
- Interactive whiteboard or touchscreen
- Classroom with space for movement activities
- Individual devices for puzzle sections (optional)

## 📚 Curriculum Alignment

### Unit 6B Content Coverage
- Direct vs. reported speech transformations
- Time expression changes (today→that day, etc.)
- Person pronoun changes (I→he/she, etc.)
- Verb tense changes (will→would, can→could)
- Question reporting techniques
- Natural reporting expressions

### Cambridge B1 Skills
- **Speaking:** Fluent presentation and interaction
- **Listening:** Comprehension of reported information
- **Grammar:** Accurate reported speech usage
- **Vocabulary:** News and reporting terminology

## 🎪 Customization Options

### Easy Modifications
- Adjust timer durations in `script.js`
- Change team sizes for projects
- Modify puzzle difficulty levels
- Add more conversation stations

### Content Adaptations
- Replace celebrity examples with local figures
- Use school-specific events for news reports
- Adapt language complexity for different levels
- Include cultural references relevant to students

## 🏆 Success Tips

### For Teachers
- Model enthusiastic news reporter voice
- Encourage risk-taking over perfection
- Celebrate "aha moments" loudly
- Use positive reinforcement consistently

### For Students
- Focus on communication over grammar perfection
- Use the sample language bank frequently
- Practice reporter voice and confidence
- Help classmates during group activities

## 🔄 Extension Activities

### Immediate Follow-ups
- Create school news bulletin using reported speech
- Interview family members and report conversations
- Watch news broadcasts and identify reported speech
- Role-play different reporter scenarios

### Long-term Projects
- Weekly news show presentations
- Investigative reporting on school topics
- Celebrity interview series
- Community event coverage

## 📞 Support & Troubleshooting

### Common Issues
- **Timer not working:** Refresh browser page
- **Buttons not responding:** Check JavaScript is enabled
- **Audio not playing:** Check browser audio permissions
- **Layout issues:** Try different browser or zoom level

### Best Practices
- Test all interactive elements before class
- Have backup activities ready
- Encourage peer support during activities
- Maintain energy and enthusiasm throughout

---

**Created for Vietnamese B1 English learners with focus on interactive, engaging, and practical reported speech learning through news reporting context.**

🌟 **Remember:** The goal is fluency and confidence, not perfection!
