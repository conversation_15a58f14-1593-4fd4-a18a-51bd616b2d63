* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    background: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

header h1 {
    color: #4a5568;
    font-size: 2.5em;
    margin-bottom: 10px;
}

header h2 {
    color: #718096;
    font-size: 1.2em;
    margin-bottom: 15px;
}

.timer {
    background: #ff6b6b;
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    font-size: 1.5em;
    font-weight: bold;
    display: inline-block;
}

.lesson-nav {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.nav-btn {
    background: white;
    border: none;
    padding: 12px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1em;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.nav-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.nav-btn.active {
    background: #4299e1;
    color: white;
}

.lesson-section {
    display: none;
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.lesson-section.active {
    display: block;
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.lesson-section h2 {
    color: #2d3748;
    font-size: 2em;
    margin-bottom: 20px;
    text-align: center;
}

.activity-box {
    background: #f7fafc;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
}

.news-clip {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
    border-left: 5px solid #4299e1;
}

.reporter-avatar {
    font-size: 2em;
    margin-bottom: 10px;
}

.speech-bubble {
    padding: 15px;
    border-radius: 20px;
    margin: 10px 0;
    position: relative;
}

.speech-bubble.direct {
    background: #e6fffa;
    border: 2px solid #38b2ac;
}

.speech-bubble.reported {
    background: #fff5f5;
    border: 2px solid #f56565;
}

.reveal-btn {
    background: #4299e1;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
}

.reveal-btn:hover {
    background: #3182ce;
    transform: scale(1.05);
}

.hidden {
    display: none;
}

.discovery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.rule-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 15px;
    text-align: center;
}

.rule-card h3 {
    margin-bottom: 15px;
    font-size: 1.3em;
}

.transformation {
    background: rgba(255,255,255,0.2);
    border-radius: 10px;
    padding: 15px;
}

.before {
    background: rgba(255,255,255,0.3);
    padding: 8px;
    margin: 5px 0;
    border-radius: 5px;
    font-weight: bold;
}

.interactive-demo {
    background: #edf2f7;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
    text-align: center;
}

.demo-sentence {
    font-size: 1.2em;
    margin: 15px 0;
}

.direct-quote {
    color: #38b2ac;
    font-weight: bold;
}

.blank-fill {
    background: #fed7d7;
    padding: 5px 10px;
    border-radius: 5px;
    min-width: 200px;
    display: inline-block;
}

.conversation-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.card {
    background: white;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    border-color: #4299e1;
}

.card h4 {
    color: #2d3748;
    margin-bottom: 10px;
}

.report-template {
    background: #f0fff4;
    border: 1px solid #68d391;
    border-radius: 5px;
    padding: 10px;
    margin-top: 10px;
    font-style: italic;
}

.puzzle-container {
    background: #f7fafc;
    border-radius: 15px;
    padding: 25px;
}

.puzzle {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}

.clues {
    margin: 20px 0;
}

.clue {
    background: #e6fffa;
    border-left: 4px solid #38b2ac;
    padding: 10px;
    margin: 10px 0;
    border-radius: 5px;
}

.quote-options {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin: 15px 0;
}

.quote-btn {
    background: #edf2f7;
    border: 2px solid #cbd5e0;
    padding: 15px;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.quote-btn:hover {
    background: #e2e8f0;
    border-color: #4299e1;
}

.quote-btn.correct {
    background: #c6f6d5;
    border-color: #38a169;
}

.quote-btn.incorrect {
    background: #fed7d7;
    border-color: #e53e3e;
}

.time-puzzle, .question-puzzle {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
    border: 2px solid #e2e8f0;
}

.scenario {
    margin: 15px 0;
}

.scenario h4 {
    color: #2d3748;
    margin-bottom: 15px;
    padding: 10px;
    background: #edf2f7;
    border-radius: 5px;
}

.wrong-report {
    background: #fed7d7;
    border: 2px solid #e53e3e;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
    font-weight: bold;
}

.fix-options, .transform-options {
    margin: 15px 0;
}

.fix-btn, .transform-btn {
    background: #f7fafc;
    border: 2px solid #cbd5e0;
    padding: 12px 15px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 5px 0;
    width: 100%;
    text-align: left;
}

.fix-btn:hover, .transform-btn:hover {
    background: #edf2f7;
    border-color: #4299e1;
    transform: translateX(5px);
}

.fix-btn.correct, .transform-btn.correct {
    background: #c6f6d5;
    border-color: #38a169;
    color: #2f855a;
}

.fix-btn.incorrect, .transform-btn.incorrect {
    background: #fed7d7;
    border-color: #e53e3e;
    color: #c53030;
}

.original-question {
    background: #e6fffa;
    border: 2px solid #38b2ac;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
    font-weight: bold;
    text-align: center;
}

.project-instructions {
    margin: 20px 0;
}

.project-option {
    background: #f0fff4;
    border: 2px solid #68d391;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}

.project-option h4 {
    color: #2f855a;
    margin-bottom: 10px;
}

.language-bank {
    background: #edf2f7;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
}

.phrase-category {
    margin: 15px 0;
}

.phrase-category h4 {
    color: #2d3748;
    margin-bottom: 10px;
}

.phrase-category ul {
    list-style: none;
}

.phrase-category li {
    background: white;
    padding: 8px 12px;
    margin: 5px 0;
    border-radius: 5px;
    border-left: 3px solid #4299e1;
}

.evaluation-criteria {
    margin: 20px 0;
}

.criteria-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 15px 0;
}

.criterion {
    background: #f0fff4;
    border: 1px solid #68d391;
    border-radius: 10px;
    padding: 15px;
    text-align: center;
}

.criterion h5 {
    color: #2f855a;
    margin-bottom: 8px;
}

.news-broadcast {
    background: #1a202c;
    color: white;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
    font-family: 'Courier New', monospace;
}

.news-broadcast > div {
    margin: 15px 0;
    padding: 10px;
    border-radius: 5px;
}

.anchor {
    background: rgba(66, 153, 225, 0.3);
}

.reporter {
    background: rgba(72, 187, 120, 0.3);
}

.field-reporter {
    background: rgba(237, 137, 54, 0.3);
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2em;
    }
    
    .lesson-nav {
        flex-direction: column;
        align-items: center;
    }
    
    .nav-btn {
        width: 200px;
    }
    
    .discovery-grid {
        grid-template-columns: 1fr;
    }
    
    .conversation-cards {
        grid-template-columns: 1fr;
    }
    
    .criteria-grid {
        grid-template-columns: 1fr;
    }
}
