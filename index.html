<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reported Speech Adventure - Unit 6B</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🎤 News Reporter Challenge</h1>
            <h2>Unit 6B: Reported Speech Adventure</h2>
            <div class="timer" id="timer">90:00</div>
        </header>

        <nav class="lesson-nav">
            <button class="nav-btn active" onclick="showSection('warmup')">🔥 Warm-up</button>
            <button class="nav-btn" onclick="showSection('discovery')">🔍 Discovery</button>
            <button class="nav-btn" onclick="showSection('practice')">💪 Practice</button>
            <button class="nav-btn" onclick="showSection('puzzles')">🧩 Puzzles</button>
            <button class="nav-btn" onclick="showSection('projects')">📺 Projects</button>
            <button class="nav-btn" onclick="showSection('presentation')">🎬 Show Time</button>
        </nav>

        <!-- WARM-UP SECTION (10 minutes) -->
        <section id="warmup" class="lesson-section active">
            <h2>🔥 News Flash Warm-up</h2>
            <div class="activity-box">
                <h3>Activity 1: What Did They Say?</h3>
                <p>Listen to these news clips and guess what the person originally said!</p>
                
                <div class="news-clip">
                    <div class="reporter-avatar">📺</div>
                    <div class="speech-bubble reported">
                        "The mayor said that he would fix the roads next month."
                    </div>
                    <button class="reveal-btn" onclick="revealOriginal(1)">What did the mayor actually say?</button>
                    <div class="original-speech hidden" id="original1">
                        <div class="speech-bubble direct">"I will fix the roads next month."</div>
                    </div>
                </div>

                <div class="news-clip">
                    <div class="reporter-avatar">📺</div>
                    <div class="speech-bubble reported">
                        "The student told me that she had finished her homework."
                    </div>
                    <button class="reveal-btn" onclick="revealOriginal(2)">What did the student actually say?</button>
                    <div class="original-speech hidden" id="original2">
                        <div class="speech-bubble direct">"I have finished my homework."</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- DISCOVERY SECTION (15 minutes) -->
        <section id="discovery" class="lesson-section">
            <h2>🔍 Reporter's Toolkit Discovery</h2>
            
            <div class="discovery-grid">
                <div class="rule-card">
                    <h3>📅 Time Changes</h3>
                    <div class="transformation">
                        <div class="before">TODAY → THAT DAY</div>
                        <div class="before">YESTERDAY → THE DAY BEFORE</div>
                        <div class="before">TOMORROW → THE NEXT DAY</div>
                    </div>
                </div>

                <div class="rule-card">
                    <h3>👥 Person Changes</h3>
                    <div class="transformation">
                        <div class="before">I → HE/SHE</div>
                        <div class="before">YOU → HE/SHE</div>
                        <div class="before">WE → THEY</div>
                    </div>
                </div>

                <div class="rule-card">
                    <h3>⏰ Verb Changes</h3>
                    <div class="transformation">
                        <div class="before">AM/IS/ARE → WAS/WERE</div>
                        <div class="before">WILL → WOULD</div>
                        <div class="before">CAN → COULD</div>
                    </div>
                </div>
            </div>

            <div class="interactive-demo">
                <h3>🎯 Try It Yourself!</h3>
                <div class="demo-sentence">
                    <p>Original: <span class="direct-quote">"I am going to the market today."</span></p>
                    <p>Reported: She said that <span class="blank-fill" id="demo-answer">___________</span></p>
                    <button onclick="showDemoAnswer()">Check Answer</button>
                </div>
            </div>
        </section>

        <!-- PRACTICE SECTION (20 minutes) -->
        <section id="practice" class="lesson-section">
            <h2>💪 Reporter Training Camp</h2>
            
            <div class="practice-activity">
                <h3>🎪 Circus of Conversations</h3>
                <p>Move around the room and collect quotes from your classmates, then report them back!</p>
                
                <div class="conversation-cards">
                    <div class="card">
                        <h4>Station 1: Future Plans</h4>
                        <p>Ask: "What will you do this weekend?"</p>
                        <div class="report-template">
                            Report: "[Name] said that he/she would..."
                        </div>
                    </div>
                    
                    <div class="card">
                        <h4>Station 2: Past Adventures</h4>
                        <p>Ask: "What did you do yesterday?"</p>
                        <div class="report-template">
                            Report: "[Name] told me that he/she had..."
                        </div>
                    </div>
                    
                    <div class="card">
                        <h4>Station 3: Opinions</h4>
                        <p>Ask: "What do you think about social media?"</p>
                        <div class="report-template">
                            Report: "[Name] said that he/she thought..."
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- PUZZLES SECTION (15 minutes) -->
        <section id="puzzles" class="lesson-section">
            <h2>🧩 Logic Puzzles: News Detective</h2>

            <div class="puzzle-container">
                <div class="puzzle" id="puzzle1">
                    <h3>🕵️ Puzzle 1: The Missing Quote</h3>
                    <p>Three students made statements. Can you figure out who said what?</p>

                    <div class="clues">
                        <div class="clue">📝 Clue 1: Nam said that he would study harder next semester.</div>
                        <div class="clue">📝 Clue 2: Linh told me that she had visited her grandmother the day before.</div>
                        <div class="clue">📝 Clue 3: Duc mentioned that he was feeling tired that day.</div>
                    </div>

                    <div class="original-quotes">
                        <h4>Original quotes (match them!):</h4>
                        <div class="quote-options">
                            <button class="quote-btn" onclick="checkMatch(1, 'A')">"I visited my grandmother yesterday."</button>
                            <button class="quote-btn" onclick="checkMatch(1, 'B')">"I will study harder next semester."</button>
                            <button class="quote-btn" onclick="checkMatch(1, 'C')">"I am feeling tired today."</button>
                        </div>
                    </div>
                </div>

                <div class="puzzle" id="puzzle2">
                    <h3>🎯 Puzzle 2: Time Travel Reporter</h3>
                    <p>You're reporting on events from different times. Fix the reporter's mistakes!</p>

                    <div class="time-puzzle">
                        <div class="scenario">
                            <h4>Scenario: Interview happened on Monday, reporting on Tuesday</h4>
                            <div class="wrong-report">
                                ❌ Wrong: "The student said 'I will finish my project tomorrow.'"
                            </div>
                            <div class="fix-options">
                                <button class="fix-btn" onclick="checkTimeFix(1, 'A')">The student said that he would finish his project tomorrow.</button>
                                <button class="fix-btn" onclick="checkTimeFix(1, 'B')">The student said that he would finish his project the next day.</button>
                                <button class="fix-btn" onclick="checkTimeFix(1, 'C')">The student said that he will finish his project the next day.</button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="puzzle" id="puzzle3">
                    <h3>🔍 Puzzle 3: Question Detective</h3>
                    <p>Transform these questions into reported speech!</p>

                    <div class="question-puzzle">
                        <div class="original-question">
                            <strong>Original:</strong> "Where do you live?" the reporter asked.
                        </div>
                        <div class="transform-options">
                            <button class="transform-btn" onclick="checkQuestionTransform(1, 'A')">The reporter asked where do you live.</button>
                            <button class="transform-btn" onclick="checkQuestionTransform(1, 'B')">The reporter asked where I lived.</button>
                            <button class="transform-btn" onclick="checkQuestionTransform(1, 'C')">The reporter asked where he lived.</button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- PROJECTS SECTION (20 minutes) -->
        <section id="projects" class="lesson-section">
            <h2>📺 News Team Projects</h2>
            
            <div class="project-instructions">
                <h3>🎬 Project Options (Choose One)</h3>
                
                <div class="project-option">
                    <h4>Project A: Breaking News Report</h4>
                    <p>Create a 3-minute news report about a school event using reported speech</p>
                    <ul>
                        <li>Interview 3 "witnesses" (classmates)</li>
                        <li>Report what they said using proper reported speech</li>
                        <li>Include: past events, future plans, and opinions</li>
                    </ul>
                </div>
                
                <div class="project-option">
                    <h4>Project B: Celebrity Interview Recap</h4>
                    <p>Imagine you interviewed a famous person and create a news summary</p>
                    <ul>
                        <li>Choose a celebrity (real or fictional)</li>
                        <li>Create an "interview" with interesting quotes</li>
                        <li>Present the recap using reported speech</li>
                    </ul>
                </div>
            </div>
            
            <div class="sample-scripts">
                <h3>📋 Sample Reporter Language</h3>
                <div class="language-bank">
                    <div class="phrase-category">
                        <h4>Opening Phrases:</h4>
                        <ul>
                            <li>"Good evening, I'm [Name] reporting from..."</li>
                            <li>"This is [Name] with breaking news about..."</li>
                            <li>"We spoke to several people who told us that..."</li>
                        </ul>
                    </div>
                    
                    <div class="phrase-category">
                        <h4>Reporting Phrases:</h4>
                        <ul>
                            <li>"According to witnesses, [person] said that..."</li>
                            <li>"[Name] told our reporter that he/she..."</li>
                            <li>"When asked about [topic], [person] mentioned that..."</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- PRESENTATION SECTION (30 minutes) -->
        <section id="presentation" class="lesson-section">
            <h2>🎬 Show Time: Live News Broadcast</h2>
            
            <div class="presentation-setup">
                <h3>📺 News Studio Setup</h3>
                <p>Each team gets 3-4 minutes to present their news report!</p>
                
                <div class="evaluation-criteria">
                    <h4>🏆 Evaluation Criteria:</h4>
                    <div class="criteria-grid">
                        <div class="criterion">
                            <h5>Comprehension</h5>
                            <p>Clear understanding of reported speech rules</p>
                        </div>
                        <div class="criterion">
                            <h5>Coherence</h5>
                            <p>Logical flow and organization</p>
                        </div>
                        <div class="criterion">
                            <h5>Fluency</h5>
                            <p>Natural delivery and confidence</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="sample-presentation">
                <h3>🌟 Sample Presentation by Team Alpha</h3>
                <div class="news-broadcast">
                    <div class="anchor">
                        <strong>Anchor (Mai):</strong> "Good evening, I'm Mai Nguyen with VTV News. Today we're reporting on the exciting school festival that took place yesterday at Nguyen Hue High School."
                    </div>
                    
                    <div class="reporter">
                        <strong>Reporter (Duc):</strong> "Thank you, Mai. I spoke with several students about the festival. Linh Tran, a grade 10 student, told me that she had never seen such amazing performances before. She said that the dance competition was her favorite part."
                    </div>
                    
                    <div class="anchor">
                        <strong>Anchor (Mai):</strong> "What about the teachers, Duc?"
                    </div>
                    
                    <div class="reporter">
                        <strong>Reporter (Duc):</strong> "Well, Mai, I also interviewed Mr. Hoang, the music teacher. He mentioned that he was very proud of his students. He told me that they had practiced for months and that the hard work had really paid off."
                    </div>
                    
                    <div class="field-reporter">
                        <strong>Field Reporter (An):</strong> "This is An reporting from the school cafeteria. I asked students about next year's festival. Most students said that they would definitely participate again. One student, Minh Le, told me that he would organize a band for next year's talent show."
                    </div>
                    
                    <div class="anchor">
                        <strong>Anchor (Mai):</strong> "Thank you for that report. It sounds like the festival was a huge success and students are already excited about next year!"
                    </div>
                </div>
            </div>
        </section>
    </div>

    <script src="script.js"></script>
</body>
</html>
