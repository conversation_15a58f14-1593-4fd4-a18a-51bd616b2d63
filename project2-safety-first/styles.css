* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    background: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

header h1 {
    color: #2E7D32;
    font-size: 2.5em;
    margin-bottom: 10px;
}

header h2 {
    color: #4CAF50;
    font-size: 1.2em;
    margin-bottom: 15px;
}

.timer {
    background: #FF5722;
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    font-size: 1.5em;
    font-weight: bold;
    display: inline-block;
}

/* Navigation */
.lesson-nav {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.nav-btn {
    background: white;
    border: none;
    padding: 12px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1em;
    font-weight: bold;
    color: #2E7D32;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.nav-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    background: #E8F5E8;
}

.nav-btn.active {
    background: #4CAF50;
    color: white;
}

/* Sections */
.lesson-section {
    display: none;
    background: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.lesson-section.active {
    display: block;
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.lesson-section h2 {
    color: #2E7D32;
    font-size: 2em;
    margin-bottom: 20px;
    text-align: center;
}

/* Activity Box */
.activity-box {
    background: #F1F8E9;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
}

.activity-box h3 {
    color: #2E7D32;
    margin-bottom: 15px;
    font-size: 1.5em;
}

/* Safety Scenarios */
.safety-scenario {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 15px;
    border-left: 5px solid #4CAF50;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.scenario-image {
    font-size: 3em;
    text-align: center;
    margin-bottom: 10px;
}

.scenario-text {
    font-size: 1.1em;
    margin-bottom: 15px;
    text-align: center;
    color: #333;
}

.safety-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 10px;
}

.safe-btn, .unsafe-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
}

.safe-btn {
    background: #4CAF50;
    color: white;
}

.safe-btn:hover {
    background: #45a049;
    transform: scale(1.05);
}

.unsafe-btn {
    background: #F44336;
    color: white;
}

.unsafe-btn:hover {
    background: #da190b;
    transform: scale(1.05);
}

.safety-feedback {
    background: #E3F2FD;
    padding: 15px;
    border-radius: 10px;
    margin-top: 10px;
    border-left: 4px solid #2196F3;
}

.safety-feedback.hidden {
    display: none;
}

/* Discovery Grid */
.discovery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.rule-card {
    background: #F1F8E9;
    border-radius: 15px;
    padding: 25px;
    text-align: center;
    box-shadow: 0 6px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.rule-card:hover {
    transform: translateY(-5px);
}

.rule-card h3 {
    color: #2E7D32;
    margin-bottom: 15px;
    font-size: 1.3em;
}

.safety-tips {
    text-align: left;
}

.tip {
    padding: 8px 0;
    font-size: 1em;
    border-bottom: 1px solid #E0E0E0;
}

.tip:last-child {
    border-bottom: none;
}

/* Interactive Demo */
.interactive-demo {
    background: #E8F5E8;
    border-radius: 15px;
    padding: 25px;
    text-align: center;
}

.interactive-demo h3 {
    color: #2E7D32;
    margin-bottom: 20px;
}

.demo-question p {
    font-size: 1.2em;
    margin-bottom: 20px;
    color: #333;
}

.quiz-options {
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-width: 600px;
    margin: 0 auto;
}

.quiz-btn {
    background: white;
    border: 2px solid #4CAF50;
    padding: 15px 20px;
    border-radius: 10px;
    cursor: pointer;
    font-size: 1em;
    transition: all 0.3s ease;
}

.quiz-btn:hover {
    background: #4CAF50;
    color: white;
    transform: scale(1.02);
}

.quiz-feedback {
    background: #C8E6C9;
    padding: 15px;
    border-radius: 10px;
    margin-top: 15px;
    border: 2px solid #4CAF50;
}

.quiz-feedback.hidden {
    display: none;
}

/* Practice Stations */
.safety-stations {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.station {
    background: #F1F8E9;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 6px 20px rgba(0,0,0,0.1);
}

.station h4 {
    color: #2E7D32;
    margin-bottom: 15px;
    font-size: 1.2em;
}

.station p {
    margin-bottom: 15px;
    color: #666;
}

.practice-template ul {
    list-style: none;
    padding: 0;
}

.practice-template li {
    background: white;
    padding: 10px 15px;
    margin-bottom: 8px;
    border-radius: 8px;
    border-left: 4px solid #4CAF50;
}

/* Puzzle Styles */
.puzzle-container {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.puzzle {
    background: #F1F8E9;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 6px 20px rgba(0,0,0,0.1);
}

.puzzle h3 {
    color: #2E7D32;
    margin-bottom: 15px;
    text-align: center;
}

.scenario-puzzle, .phone-puzzle, .emergency-puzzle {
    text-align: center;
}

.choice-options, .phone-options, .emergency-options {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 20px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.choice-btn, .phone-btn, .emergency-btn {
    background: white;
    border: 2px solid #4CAF50;
    padding: 15px 20px;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
}

.choice-btn:hover, .phone-btn:hover, .emergency-btn:hover {
    background: #4CAF50;
    color: white;
    transform: scale(1.02);
}

.choice-feedback, .phone-feedback, .emergency-feedback {
    background: #C8E6C9;
    padding: 15px;
    border-radius: 10px;
    margin-top: 15px;
    border: 2px solid #4CAF50;
}

.choice-feedback.hidden, .phone-feedback.hidden, .emergency-feedback.hidden {
    display: none;
}

/* Project Styles */
.project-instructions {
    margin-bottom: 30px;
}

.project-instructions h3 {
    color: #2E7D32;
    margin-bottom: 20px;
    text-align: center;
}

.project-option {
    background: #F1F8E9;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    border-left: 5px solid #4CAF50;
}

.project-option h4 {
    color: #2E7D32;
    margin-bottom: 10px;
}

.project-option ul {
    margin-top: 10px;
    padding-left: 20px;
}

.project-option li {
    margin-bottom: 5px;
    color: #666;
}

/* Language Bank */
.sample-scripts {
    background: #E8F5E8;
    border-radius: 15px;
    padding: 25px;
}

.sample-scripts h3 {
    color: #2E7D32;
    margin-bottom: 20px;
    text-align: center;
}

.language-bank {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.phrase-category {
    background: white;
    border-radius: 10px;
    padding: 20px;
}

.phrase-category h4 {
    color: #2E7D32;
    margin-bottom: 15px;
}

.phrase-category ul {
    list-style: none;
    padding: 0;
}

.phrase-category li {
    background: #F1F8E9;
    padding: 8px 12px;
    margin-bottom: 8px;
    border-radius: 6px;
    border-left: 3px solid #4CAF50;
}

/* Presentation Styles */
.presentation-setup {
    margin-bottom: 30px;
}

.presentation-setup h3 {
    color: #2E7D32;
    margin-bottom: 15px;
    text-align: center;
}

.evaluation-criteria {
    background: #F1F8E9;
    border-radius: 15px;
    padding: 25px;
    margin-top: 20px;
}

.evaluation-criteria h4 {
    color: #2E7D32;
    margin-bottom: 20px;
    text-align: center;
}

.criteria-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.criterion {
    background: white;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.criterion h5 {
    color: #2E7D32;
    margin-bottom: 10px;
    font-size: 1.2em;
}

.criterion p {
    color: #666;
    font-size: 0.9em;
}

/* Sample Presentation */
.sample-presentation {
    background: #E8F5E8;
    border-radius: 15px;
    padding: 25px;
}

.sample-presentation h3 {
    color: #2E7D32;
    margin-bottom: 20px;
    text-align: center;
}

.safety-presentation {
    background: white;
    border-radius: 10px;
    padding: 25px;
}

.presenter {
    background: #F1F8E9;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
    border-left: 4px solid #4CAF50;
}

.presenter strong {
    color: #2E7D32;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .lesson-nav {
        flex-direction: column;
        align-items: center;
    }
    
    .nav-btn {
        width: 200px;
        margin-bottom: 5px;
    }
    
    .discovery-grid {
        grid-template-columns: 1fr;
    }
    
    .safety-stations {
        grid-template-columns: 1fr;
    }
    
    .criteria-grid {
        grid-template-columns: 1fr;
    }
    
    .language-bank {
        grid-template-columns: 1fr;
    }
    
    .safety-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .quiz-options, .choice-options, .phone-options, .emergency-options {
        max-width: 100%;
    }
}

/* Hidden class */
.hidden {
    display: none !important;
}
