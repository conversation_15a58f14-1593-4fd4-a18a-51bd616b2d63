<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Safety First - Interactive Learning</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🛡️ Safety First Adventure</h1>
            <h2>Stay Safe and Sound: Interactive Safety Learning</h2>
            <div class="timer" id="timer">90:00</div>
        </header>

        <nav class="lesson-nav">
            <button class="nav-btn active" onclick="showSection('warmup')">🔥 Warm-up</button>
            <button class="nav-btn" onclick="showSection('discovery')">🔍 Discovery</button>
            <button class="nav-btn" onclick="showSection('practice')">💪 Practice</button>
            <button class="nav-btn" onclick="showSection('puzzles')">🧩 Puzzles</button>
            <button class="nav-btn" onclick="showSection('projects')">📱 Projects</button>
            <button class="nav-btn" onclick="showSection('presentation')">🎬 Show Time</button>
        </nav>

        <!-- WARM-UP SECTION (10 minutes) -->
        <section id="warmup" class="lesson-section active">
            <h2>🔥 Safety Awareness Warm-up</h2>
            <div class="activity-box">
                <h3>Activity 1: Safety Scenarios</h3>
                <p>Look at these situations and decide: Safe or Unsafe?</p>
                
                <div class="safety-scenario">
                    <div class="scenario-image">🚶‍♀️</div>
                    <div class="scenario-text">
                        "Walking alone at night in a well-lit area with people around"
                    </div>
                    <div class="safety-buttons">
                        <button class="safe-btn" onclick="checkSafety(1, 'safe')">✅ Safe</button>
                        <button class="unsafe-btn" onclick="checkSafety(1, 'unsafe')">❌ Unsafe</button>
                    </div>
                    <div class="safety-feedback hidden" id="feedback1">
                        <p>⚠️ Better to avoid walking alone at night. Always travel with friends or family when possible!</p>
                    </div>
                </div>

                <div class="safety-scenario">
                    <div class="scenario-image">📱</div>
                    <div class="scenario-text">
                        "Sharing your location with strangers on social media"
                    </div>
                    <div class="safety-buttons">
                        <button class="safe-btn" onclick="checkSafety(2, 'safe')">✅ Safe</button>
                        <button class="unsafe-btn" onclick="checkSafety(2, 'unsafe')">❌ Unsafe</button>
                    </div>
                    <div class="safety-feedback hidden" id="feedback2">
                        <p>❌ Never share your location with strangers! Keep your personal information private.</p>
                    </div>
                </div>

                <div class="safety-scenario">
                    <div class="scenario-image">🚗</div>
                    <div class="scenario-text">
                        "Getting into a car with someone you don't know well"
                    </div>
                    <div class="safety-buttons">
                        <button class="safe-btn" onclick="checkSafety(3, 'safe')">✅ Safe</button>
                        <button class="unsafe-btn" onclick="checkSafety(3, 'unsafe')">❌ Unsafe</button>
                    </div>
                    <div class="safety-feedback hidden" id="feedback3">
                        <p>❌ Never get into a car with strangers! Always use trusted transportation or travel with people you know.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- DISCOVERY SECTION (15 minutes) -->
        <section id="discovery" class="lesson-section">
            <h2>🔍 Safety Rules Discovery</h2>
            
            <div class="discovery-grid">
                <div class="rule-card">
                    <h3>📱 Phone Safety</h3>
                    <div class="safety-tips">
                        <div class="tip">✅ Keep your phone charged</div>
                        <div class="tip">✅ Save emergency contacts</div>
                        <div class="tip">❌ Don't share personal info</div>
                        <div class="tip">❌ Don't meet online strangers</div>
                    </div>
                </div>

                <div class="rule-card">
                    <h3>🚶‍♀️ Street Safety</h3>
                    <div class="safety-tips">
                        <div class="tip">✅ Stay in well-lit areas</div>
                        <div class="tip">✅ Walk with confidence</div>
                        <div class="tip">✅ Be aware of surroundings</div>
                        <div class="tip">❌ Don't walk alone at night</div>
                    </div>
                </div>

                <div class="rule-card">
                    <h3>🚗 Transportation Safety</h3>
                    <div class="safety-tips">
                        <div class="tip">✅ Use trusted transportation</div>
                        <div class="tip">✅ Share your travel plans</div>
                        <div class="tip">✅ Keep valuables hidden</div>
                        <div class="tip">❌ Don't accept rides from strangers</div>
                    </div>
                </div>
            </div>

            <div class="interactive-demo">
                <h3>🎯 Safety Quiz!</h3>
                <div class="demo-question">
                    <p>Question: What should you do if someone you don't know offers you a ride?</p>
                    <div class="quiz-options">
                        <button class="quiz-btn" onclick="checkQuizAnswer(1, 'A')">A) Accept if they seem nice</button>
                        <button class="quiz-btn" onclick="checkQuizAnswer(1, 'B')">B) Politely decline and find another way</button>
                        <button class="quiz-btn" onclick="checkQuizAnswer(1, 'C')">C) Ask for their phone number first</button>
                    </div>
                    <div class="quiz-feedback hidden" id="quiz-feedback1">
                        <p>✅ Correct! Always politely decline rides from strangers and find trusted transportation.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- PRACTICE SECTION (20 minutes) -->
        <section id="practice" class="lesson-section">
            <h2>💪 Safety Skills Practice</h2>
            
            <div class="practice-activity">
                <h3>🎪 Safety Stations Challenge</h3>
                <p>Move around and practice different safety scenarios with your classmates!</p>
                
                <div class="safety-stations">
                    <div class="station">
                        <h4>Station 1: Emergency Contacts</h4>
                        <p>Practice: Set up emergency contacts in your phone</p>
                        <div class="practice-template">
                            <ul>
                                <li>Parents/Guardians</li>
                                <li>Local Police: 113</li>
                                <li>Emergency Services: 115</li>
                                <li>Trusted friend or relative</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="station">
                        <h4>Station 2: Safe Communication</h4>
                        <p>Practice: How to decline unsafe situations politely</p>
                        <div class="practice-template">
                            <ul>
                                <li>"Thank you, but I have other plans"</li>
                                <li>"I need to check with my parents first"</li>
                                <li>"I'm not comfortable with that"</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="station">
                        <h4>Station 3: Situational Awareness</h4>
                        <p>Practice: Identifying safe vs unsafe situations</p>
                        <div class="practice-template">
                            <ul>
                                <li>Look for well-lit areas</li>
                                <li>Notice who's around you</li>
                                <li>Trust your instincts</li>
                                <li>Have an exit plan</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- PUZZLES SECTION (15 minutes) -->
        <section id="puzzles" class="lesson-section">
            <h2>🧩 Safety Detective Puzzles</h2>

            <div class="puzzle-container">
                <div class="puzzle" id="puzzle1">
                    <h3>🕵️ Puzzle 1: The Safe Choice</h3>
                    <p>Read the scenario and choose the safest option:</p>

                    <div class="scenario-puzzle">
                        <div class="scenario-text">
                            "You're walking home from school and a stranger in a car offers you a ride. They say they know your parents. What do you do?"
                        </div>
                        <div class="choice-options">
                            <button class="choice-btn" onclick="checkChoice(1, 'A')">A) Get in the car since they know your parents</button>
                            <button class="choice-btn" onclick="checkChoice(1, 'B')">B) Ask them to prove they know your parents</button>
                            <button class="choice-btn" onclick="checkChoice(1, 'C')">C) Politely decline and continue walking or call your parents</button>
                        </div>
                        <div class="choice-feedback hidden" id="choice-feedback1">
                            <p>✅ Correct! Never get in a car with strangers, even if they claim to know your family. Always verify with your parents first.</p>
                        </div>
                    </div>
                </div>

                <div class="puzzle" id="puzzle2">
                    <h3>🎯 Puzzle 2: Phone Safety Challenge</h3>
                    <p>Which of these is the SAFEST way to use your phone?</p>

                    <div class="phone-puzzle">
                        <div class="phone-options">
                            <button class="phone-btn" onclick="checkPhoneSafety(1, 'A')">A) Share your location with everyone on social media</button>
                            <button class="phone-btn" onclick="checkPhoneSafety(1, 'B')">B) Only share personal info with close friends and family</button>
                            <button class="phone-btn" onclick="checkPhoneSafety(1, 'C')">C) Give your phone number to anyone who asks</button>
                        </div>
                        <div class="phone-feedback hidden" id="phone-feedback1">
                            <p>✅ Correct! Only share personal information with people you trust completely.</p>
                        </div>
                    </div>
                </div>

                <div class="puzzle" id="puzzle3">
                    <h3>🔍 Puzzle 3: Emergency Situation</h3>
                    <p>You feel unsafe in a public place. What's your best first step?</p>

                    <div class="emergency-puzzle">
                        <div class="emergency-options">
                            <button class="emergency-btn" onclick="checkEmergency(1, 'A')">A) Ignore the feeling and continue</button>
                            <button class="emergency-btn" onclick="checkEmergency(1, 'B')">B) Move to a safe, public area with other people</button>
                            <button class="emergency-btn" onclick="checkEmergency(1, 'C')">C) Confront the source of danger</button>
                        </div>
                        <div class="emergency-feedback hidden" id="emergency-feedback1">
                            <p>✅ Correct! Trust your instincts and move to a safe area with other people around.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- PROJECTS SECTION (20 minutes) -->
        <section id="projects" class="lesson-section">
            <h2>📱 Safety Campaign Projects</h2>
            
            <div class="project-instructions">
                <h3>🎬 Project Options (Choose One)</h3>
                
                <div class="project-option">
                    <h4>Project A: Safety Awareness Video</h4>
                    <p>Create a 3-minute safety awareness video for your school</p>
                    <ul>
                        <li>Choose one safety topic (phone, street, or transport safety)</li>
                        <li>Include do's and don'ts</li>
                        <li>Show real-life scenarios and solutions</li>
                        <li>Make it engaging and memorable</li>
                    </ul>
                </div>
                
                <div class="project-option">
                    <h4>Project B: Safety Tips Poster Campaign</h4>
                    <p>Design an interactive poster series about staying safe</p>
                    <ul>
                        <li>Create 3 different safety posters</li>
                        <li>Include clear, actionable tips</li>
                        <li>Use eye-catching visuals and colors</li>
                        <li>Present your campaign to the class</li>
                    </ul>
                </div>

                <div class="project-option">
                    <h4>Project C: Safety Role-Play Scenarios</h4>
                    <p>Create and perform safety scenarios for the class</p>
                    <ul>
                        <li>Develop 2-3 realistic safety scenarios</li>
                        <li>Show both unsafe and safe responses</li>
                        <li>Engage the audience in discussion</li>
                        <li>Provide practical safety tips</li>
                    </ul>
                </div>
            </div>
            
            <div class="sample-scripts">
                <h3>📋 Safety Language Bank</h3>
                <div class="language-bank">
                    <div class="phrase-category">
                        <h4>Warning Phrases:</h4>
                        <ul>
                            <li>"Be careful when..."</li>
                            <li>"Never share your..."</li>
                            <li>"Always make sure to..."</li>
                            <li>"If you feel unsafe..."</li>
                        </ul>
                    </div>
                    
                    <div class="phrase-category">
                        <h4>Safety Advice:</h4>
                        <ul>
                            <li>"The safest option is to..."</li>
                            <li>"Remember to always..."</li>
                            <li>"In case of emergency..."</li>
                            <li>"Trust your instincts and..."</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- PRESENTATION SECTION (30 minutes) -->
        <section id="presentation" class="lesson-section">
            <h2>🎬 Show Time: Safety First Presentations</h2>
            
            <div class="presentation-setup">
                <h3>📺 Safety Campaign Showcase</h3>
                <p>Each team gets 3-4 minutes to present their safety campaign!</p>
                
                <div class="evaluation-criteria">
                    <h4>🏆 Evaluation Criteria:</h4>
                    <div class="criteria-grid">
                        <div class="criterion">
                            <h5>Comprehension</h5>
                            <p>Clear understanding of safety concepts and practical application</p>
                        </div>
                        <div class="criterion">
                            <h5>Coherence</h5>
                            <p>Logical organization and clear message delivery</p>
                        </div>
                        <div class="criterion">
                            <h5>Fluency</h5>
                            <p>Confident presentation and natural communication</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="sample-presentation">
                <h3>🌟 Sample Presentation by Team Safety Stars</h3>
                <div class="safety-presentation">
                    <div class="presenter">
                        <strong>Presenter 1 (Linh):</strong> "Good morning everyone! We're Team Safety Stars, and today we want to talk about staying safe while using technology."
                    </div>
                    
                    <div class="presenter">
                        <strong>Presenter 2 (Nam):</strong> "Did you know that sharing too much personal information online can put you at risk? We should never share our home address, phone number, or school schedule with strangers."
                    </div>
                    
                    <div class="presenter">
                        <strong>Presenter 3 (Mai):</strong> "Here are three simple rules: First, keep your personal information private. Second, never meet someone you only know online. Third, always tell a trusted adult if someone makes you feel uncomfortable."
                    </div>
                    
                    <div class="presenter">
                        <strong>Presenter 1 (Linh):</strong> "Remember, being safe doesn't mean being scared. It means being smart! Let's all work together to create a safer community for everyone."
                    </div>
                    
                    <div class="presenter">
                        <strong>All Together:</strong> "Stay safe, stay smart, safety first!"
                    </div>
                </div>
            </div>
        </section>
    </div>

    <script src="script.js"></script>
</body>
</html>
