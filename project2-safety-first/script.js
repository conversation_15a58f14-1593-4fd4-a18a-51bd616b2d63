// Timer functionality
let timeLeft = 90 * 60; // 90 minutes in seconds
let timerInterval;

function startTimer() {
    timerInterval = setInterval(() => {
        timeLeft--;
        updateTimerDisplay();
        
        if (timeLeft <= 0) {
            clearInterval(timerInterval);
            alert("Time's up! Great job learning about safety! 🛡️");
        }
    }, 1000);
}

function updateTimerDisplay() {
    const minutes = Math.floor(timeLeft / 60);
    const seconds = timeLeft % 60;
    const display = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    document.getElementById('timer').textContent = display;
    
    // Change color as time runs out
    const timerElement = document.getElementById('timer');
    if (timeLeft < 600) { // Last 10 minutes
        timerElement.style.background = '#F44336';
    } else if (timeLeft < 1800) { // Last 30 minutes
        timerElement.style.background = '#FF9800';
    }
}

// Section navigation
function showSection(sectionId) {
    // Hide all sections
    const sections = document.querySelectorAll('.lesson-section');
    sections.forEach(section => {
        section.classList.remove('active');
    });
    
    // Remove active class from all nav buttons
    const navButtons = document.querySelectorAll('.nav-btn');
    navButtons.forEach(btn => {
        btn.classList.remove('active');
    });
    
    // Show selected section
    document.getElementById(sectionId).classList.add('active');
    
    // Add active class to clicked button
    event.target.classList.add('active');
}

// Safety scenario checking
function checkSafety(scenarioId, choice) {
    const feedback = document.getElementById(`feedback${scenarioId}`);
    feedback.classList.remove('hidden');
    
    // Add some visual feedback
    const scenario = feedback.parentElement;
    if (choice === 'unsafe' && (scenarioId === 2 || scenarioId === 3)) {
        // Correct answers for scenarios 2 and 3
        scenario.style.borderLeft = '5px solid #4CAF50';
        feedback.style.background = '#C8E6C9';
        feedback.style.borderLeft = '4px solid #4CAF50';
    } else if (choice === 'safe' && scenarioId === 1) {
        // Partially correct for scenario 1
        scenario.style.borderLeft = '5px solid #FF9800';
        feedback.style.background = '#FFE0B2';
        feedback.style.borderLeft = '4px solid #FF9800';
    } else {
        // Incorrect answer
        scenario.style.borderLeft = '5px solid #F44336';
        feedback.style.background = '#FFCDD2';
        feedback.style.borderLeft = '4px solid #F44336';
    }
}

// Quiz answer checking
function checkQuizAnswer(questionId, answer) {
    const feedback = document.getElementById(`quiz-feedback${questionId}`);
    feedback.classList.remove('hidden');
    
    if (answer === 'B') {
        feedback.innerHTML = '<p>✅ Correct! Always politely decline rides from strangers and find trusted transportation.</p>';
        feedback.style.background = '#C8E6C9';
        feedback.style.borderColor = '#4CAF50';
    } else {
        feedback.innerHTML = '<p>❌ Not the safest choice. Always politely decline rides from strangers and find trusted transportation.</p>';
        feedback.style.background = '#FFCDD2';
        feedback.style.borderColor = '#F44336';
    }
}

// Choice checking for puzzles
function checkChoice(puzzleId, choice) {
    const feedback = document.getElementById(`choice-feedback${puzzleId}`);
    feedback.classList.remove('hidden');
    
    if (choice === 'C') {
        feedback.innerHTML = '<p>✅ Correct! Never get in a car with strangers, even if they claim to know your family. Always verify with your parents first.</p>';
        feedback.style.background = '#C8E6C9';
        feedback.style.borderColor = '#4CAF50';
    } else {
        feedback.innerHTML = '<p>❌ Not the safest choice. Never get in a car with strangers. Always politely decline and verify with your parents first.</p>';
        feedback.style.background = '#FFCDD2';
        feedback.style.borderColor = '#F44336';
    }
}

// Phone safety checking
function checkPhoneSafety(puzzleId, choice) {
    const feedback = document.getElementById(`phone-feedback${puzzleId}`);
    feedback.classList.remove('hidden');
    
    if (choice === 'B') {
        feedback.innerHTML = '<p>✅ Correct! Only share personal information with people you trust completely.</p>';
        feedback.style.background = '#C8E6C9';
        feedback.style.borderColor = '#4CAF50';
    } else {
        feedback.innerHTML = '<p>❌ Not safe! Never share personal information with strangers. Only share with people you trust completely.</p>';
        feedback.style.background = '#FFCDD2';
        feedback.style.borderColor = '#F44336';
    }
}

// Emergency situation checking
function checkEmergency(puzzleId, choice) {
    const feedback = document.getElementById(`emergency-feedback${puzzleId}`);
    feedback.classList.remove('hidden');
    
    if (choice === 'B') {
        feedback.innerHTML = '<p>✅ Correct! Trust your instincts and move to a safe area with other people around.</p>';
        feedback.style.background = '#C8E6C9';
        feedback.style.borderColor = '#4CAF50';
    } else {
        feedback.innerHTML = '<p>❌ Not the best choice. Always trust your instincts and move to a safe, public area with other people.</p>';
        feedback.style.background = '#FFCDD2';
        feedback.style.borderColor = '#F44336';
    }
}

// Add some interactive animations
function addClickAnimation(element) {
    element.style.transform = 'scale(0.95)';
    setTimeout(() => {
        element.style.transform = 'scale(1)';
    }, 150);
}

// Add click animations to all buttons
document.addEventListener('DOMContentLoaded', function() {
    // Start the timer
    startTimer();
    
    // Add click animations to buttons
    const buttons = document.querySelectorAll('button');
    buttons.forEach(button => {
        button.addEventListener('click', function() {
            addClickAnimation(this);
        });
    });
    
    // Add hover effects to cards
    const cards = document.querySelectorAll('.rule-card, .station, .puzzle');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
    
    // Add progress tracking
    trackProgress();
});

// Progress tracking
function trackProgress() {
    let completedActivities = 0;
    const totalActivities = 6; // Number of main activities
    
    // Track section visits
    const navButtons = document.querySelectorAll('.nav-btn');
    navButtons.forEach(button => {
        button.addEventListener('click', function() {
            if (!this.classList.contains('visited')) {
                this.classList.add('visited');
                completedActivities++;
                updateProgressDisplay();
            }
        });
    });
}

function updateProgressDisplay() {
    // Could add a progress bar here if needed
    console.log('Progress updated');
}

// Safety tips popup (bonus feature)
function showSafetyTip() {
    const tips = [
        "🛡️ Always trust your instincts - if something feels wrong, it probably is!",
        "📱 Keep your personal information private online and offline.",
        "👥 Travel with friends or family whenever possible, especially at night.",
        "🚨 Know your emergency contacts and keep your phone charged.",
        "🏠 Always let someone know where you're going and when you'll be back.",
        "🚗 Never accept rides from strangers, even if they seem friendly.",
        "💡 Stay in well-lit, public areas when you're out and about.",
        "🤝 It's okay to say 'no' to anything that makes you uncomfortable."
    ];
    
    const randomTip = tips[Math.floor(Math.random() * tips.length)];
    
    // Create a temporary popup
    const popup = document.createElement('div');
    popup.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #4CAF50;
        color: white;
        padding: 15px 20px;
        border-radius: 10px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        z-index: 1000;
        max-width: 300px;
        font-size: 14px;
        animation: slideIn 0.5s ease-out;
    `;
    
    popup.innerHTML = `
        <strong>💡 Safety Tip:</strong><br>
        ${randomTip}
        <button onclick="this.parentElement.remove()" style="
            background: none;
            border: none;
            color: white;
            float: right;
            cursor: pointer;
            font-size: 16px;
            margin-top: -5px;
        ">×</button>
    `;
    
    document.body.appendChild(popup);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (popup.parentElement) {
            popup.remove();
        }
    }, 5000);
}

// Show a safety tip every 3 minutes
setInterval(showSafetyTip, 180000);

// Add CSS animation for popup
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
`;
document.head.appendChild(style);

// Keyboard navigation support
document.addEventListener('keydown', function(e) {
    if (e.key === 'ArrowRight' || e.key === 'ArrowLeft') {
        const activeButton = document.querySelector('.nav-btn.active');
        const navButtons = Array.from(document.querySelectorAll('.nav-btn'));
        const currentIndex = navButtons.indexOf(activeButton);
        
        let nextIndex;
        if (e.key === 'ArrowRight') {
            nextIndex = (currentIndex + 1) % navButtons.length;
        } else {
            nextIndex = (currentIndex - 1 + navButtons.length) % navButtons.length;
        }
        
        navButtons[nextIndex].click();
    }
});

// Add accessibility features
function addAccessibilityFeatures() {
    // Add ARIA labels to buttons
    const buttons = document.querySelectorAll('button');
    buttons.forEach(button => {
        if (!button.getAttribute('aria-label')) {
            button.setAttribute('aria-label', button.textContent);
        }
    });
    
    // Add role attributes to interactive elements
    const interactiveElements = document.querySelectorAll('.safety-scenario, .puzzle, .station');
    interactiveElements.forEach(element => {
        element.setAttribute('role', 'region');
        element.setAttribute('tabindex', '0');
    });
}

// Initialize accessibility features when page loads
document.addEventListener('DOMContentLoaded', addAccessibilityFeatures);

// Sound effects (optional - can be enabled if audio files are available)
function playSound(soundType) {
    // This would play sound effects for correct/incorrect answers
    // Implementation would require audio files
    console.log(`Playing ${soundType} sound`);
}

// Celebration animation for correct answers
function celebrateCorrectAnswer(element) {
    element.style.animation = 'bounce 0.6s ease-in-out';
    setTimeout(() => {
        element.style.animation = '';
    }, 600);
}

// Add bounce animation CSS
const bounceStyle = document.createElement('style');
bounceStyle.textContent = `
    @keyframes bounce {
        0%, 20%, 60%, 100% {
            transform: translateY(0);
        }
        40% {
            transform: translateY(-10px);
        }
        80% {
            transform: translateY(-5px);
        }
    }
`;
document.head.appendChild(bounceStyle);
